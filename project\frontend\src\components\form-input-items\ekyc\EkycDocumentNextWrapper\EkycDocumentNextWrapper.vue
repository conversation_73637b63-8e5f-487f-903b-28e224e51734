<template>
  <component :is="element.layout" :element="element" @visible="onVisible">
    <div class="ekyc-main">
      <!-- Support document trigger -->
      <div v-if="showAutoDetectHelp" @click="onTriggerNotice" class="support-document-trigger">
        <ReactiveIcon
          icon="lucide:circle-question-mark"
          width="22"
          height="22"
          class="result-desc-icon"
          style="width: 22px; height: 22px"
        />
      </div>

      <TheModal
        :is-active="showSupportDocumentTypeModal"
        :can-close="true"
        class="support-document-modal"
        style="z-index: 100000"
        @close="showSupportDocumentTypeModal = false"
      >
        <template #title>
          <span>{{ t('ekyc.document.supported_documents') }}</span>
        </template>
        <template #body>
          <EkycSupportedDocumentsBox :choices="fullChoices" />
        </template>
        <template #footer> <div></div></template>
      </TheModal>

      <!-- Document Modal -->
      <div id="main-container">
        <EkycDocMediaModal
          v-show="currentStep === 'document'"
          ref="ekycComponent"
          :setting="extendedSettingForEachType"
          :media="'document'"
          :item-type="selectorValue"
          :selected-country="selectedCountry"
          :accepted-document-types="fullChoices.map(item => item.value)"
          @ready="readyHandler"
          @camera-ready="cameraReadyHandler"
          @camera-check="cameraCheckHandler"
          @captured="documentCapturedHandler"
          @uploaded="e => documentUploadedHandler('document', e)"
          @error="e => errorHandler('document', e)"
          @close="closeHandler"
          @loading="loadingHandler"
          @set-preview="e => setPreview('document', e)"
        >
          <template v-if="!autoDetectDocumentType" #selector>
            <div class="header-title mb-[26px]">
              {{ headerText }}
            </div>
            <DynamicComponent
              v-for="(schema, i) in childrenSchemas"
              ref="children"
              :key="'child' + i"
              :element="schema"
            />
          </template>

          <!-- <template v-else #selector>
            <div class="modal is-active !justify-start">
              <div class="header-title mb-[26px] mt-24 !text-white">
                {{ t('ekyc.document.supported_documents') }}
              </div>
              <a
                class="close-btn fixed top-4 right-2 text-white z-10"
                @click="onCloseSupportedDocuments"
                @keydown.esc="onCloseSupportedDocuments"
              >
                <ReactiveIcon icon="lucide:x" width="30" heigth="30" class="!w-8 !h-8" />
              </a>
              <div class="modal-background !bg-[#283039db]" />
              <div class="w-10/12 mx-10 px-3 py-1.5 bg-white rounded-[8px] z-20 overflow-auto">
                <EkycSupportedDocumentsBox :choices="fullChoices" />
              </div>
            </div>
          </template> -->

          <template v-if="!autoDetectDocumentType" #selector-trigger>
            <div
              class="selector-trigger"
              :class="canGoBackSelector ? 'cursor-pointer' : ''"
              @click="canGoBackSelector && goBackToSelector()"
            >
              <div class="flex flex-row items-center gap-3">
                <ReactiveIcon :icon="selectorDocumentCountry?.country?.icon" width="24px" />
                <div class="vertical-divider"></div>
                <div class="w-6 h-6">
                  <img
                    v-if="selectedDocumentChoice.image"
                    :src="selectedDocumentChoice.image"
                    alt=""
                    class="choice-image"
                  />
                  <ImageSelector v-else :name="selectedDocumentChoice.icon" class="choice-icon" />
                </div>
                <span>{{ selectorDocumentCountry?.document?.label || '' }}</span>
              </div>
              <ReactiveIcon
                v-if="canGoBackSelector"
                icon="lucide:menu"
                width="24px"
                class="dropdown-icon"
              />
            </div>
          </template>

          <!-- <template v-else #selector-trigger>
            <div class="selector-trigger cursor-pointer" @click="goBackToSelector">
              <div class="flex flex-row items-center gap-3">
                <ReactiveIcon icon="lucide:credit-card" width="24px" class="text-[#777B8B]" />
                <span>{{ t('ekyc.document.view_supported_documents') }}</span>
              </div>
              <ReactiveIcon icon="lucide:arrow-right" width="24px" class="dropdown-icon" />
            </div>
          </template> -->
        </EkycDocMediaModal>

        <!-- Backcard Wrapper -->
        <EkycBackCardWrapperNext
          v-if="includeBackcard && backcardSchema"
          v-show="currentStep === 'backcard'"
          ref="backcardComponentRef"
          :element="backcardSchema"
          @retake="goToTakeBackcard"
          @remove-action-hash="removeActionHash"
          @item-captured="backcardCapturedHandler"
          @item-uploaded="e => documentUploadedHandler('backcard', e)"
          @item-error="e => errorHandler('backcard', e)"
          @item-set-preview="e => setPreview('backcard', e)"
        />

        <EkycContentValidate
          ref="validateComponentRef"
          media="document"
          :preview="preview"
          :include-backcard="includeBackcard"
          :is-auto-detect-document-type="autoDetectDocumentType"
          :document-type-from-auto-detect="documentTypeFromAutoDetect"
          @trigger-notice="onTriggerNotice"
        />

        <!-- <div
          v-if="
            autoDetectDocumentType &&
            ['failed_backend', 'failed_others'].includes(preview.document.resultState)
          "
          class="mt-4"
        >
          <EkycSupportedDocumentsBox :choices="fullChoices" />
        </div> -->

        <!-- Card Flip -->
        <EkycCardFlipOverlay v-if="overlayPopupState === 'card_flip'" />

        <!-- Error Camera Permission -->
        <EkycPermissionOverlay
          v-if="overlayPopupState === 'camera_permission'"
          :override-images="element?.override_images"
        />

        <!-- Camera Retry -->
        <EkycCameraRetryOverlay
          v-if="overlayPopupState === 'camera_retry'"
          :override-images="element?.override_images"
          @yes="retryCamera"
        />

        <!-- Max Attempt -->
        <EkycMaxAttemptOverlay
          v-if="overlayPopupState === 'max_attempt'"
          :max-attempt-setting="element.max_attempt_warning"
        />

        <!-- Blocking Error -->
        <EkycBlockingErrorOverlay
          v-if="overlayPopupState === 'blocking_error'"
          :overlay-setting="element.blocking_error"
        />

        <!-- Unfocus, Switch App -->
        <EkycRetryOverlay
          v-if="overlayPopupState === 'unfocus_app'"
          @yes="restart"
          style="background-color: white"
        >
          <template #image>
            <img
              v-if="element?.override_images?.['failed-timeout']"
              :src="element?.override_images?.['failed-timeout']"
              class="preview"
              alt="failed"
            />
            <ImageSelector v-else name="failed-timeout" />
          </template>
          <template #title>{{ t('ekyc.recorder.error_liveness_unfocus_app') }}</template>
          <template #desc>{{ t('ekyc.recorder.error_liveness_unfocus_app_desc') }}</template>
        </EkycRetryOverlay>

        <!-- Loader -->
        <LoadingWrapper
          v-if="overlayPopupState === 'loading'"
          class="m-auto text-2xl w-full absolute"
          background-color="white"
        />
      </div>
    </div>

    <ConfirmCancelModal
      :question="t('ekyc.front_card.cancel')"
      :is-active="isConfirmCancelModalActive"
      @yes="() => onClickModalAction(true)"
      @no="() => onClickModalAction(false)"
    />

    <!-- DEBUG -->
    <div
      v-if="isSuperUser()"
      :class="[
        'debug-overlay fixed z-[100000] bg-black/10 bottom-0 left-0 right-0',
        'hover:opacity-100 transition-all',
        [showAllDebugButtons ? 'opacity-50' : 'opacity-0'],
      ]"
    >
      <div class="flex flex-wrap gap-1 text-xs">
        <button class="button is-primary" @click="showAllDebugButtons = !showAllDebugButtons">
          {}
        </button>
        <template v-if="showAllDebugButtons">
          <button
            :class="debugMode ? 'bg-green-300' : 'bg-white/50'"
            @click="debugMode = !debugMode"
          >
            DEBUG
          </button>
          <button
            class="bg-pink-300"
            :class="maxAttemptValue ? 'bg-green-300' : 'bg-white/50'"
            @click="forceToggleMaxAttempt()"
          >
            Max attempt
          </button>
          <button class="bg-blue-300" @click="forceCrossDevice()">Cross Device</button>
          <button class="bg-yellow-300" @click="restart">Restart</button>
          <div v-for="(p, key) in preview" class="flex gap-1">
            <span class="text-white">{{ key }}</span>
            <button
              v-for="st in POSSIBLE_RESULT_STATE"
              :key="st"
              :class="p.resultState === st ? '!bg-green-300' : 'bg-white/50'"
              @click="p.resultState = st"
            >
              {{ st }}
            </button>
          </div>
        </template>
      </div>
    </div>
  </component>
</template>
<script setup lang="ts">
import { WatchDebouncedOptions, promiseTimeout, templateRef, watchDebounced } from '@vueuse/core';
import get from 'lodash/get';

import DynamicComponent from '@core/components/dynamic-form/DynamicComponent.vue';
import { useCommonChoiceItem } from '@core/composables/use-common-choice-item';
import { getCountryIcon } from '@core/helpers/address/countries';

import ImageSelector from '@helpers/components/ImageSelector.vue';
import LoadingWrapper from '@helpers/components/LoadingWrapper.vue';
import ReactiveIcon from '@helpers/components/ReactiveIcon.vue';
import { useI18n } from '@helpers/helpers/vue-i18n-composable';

import ConfirmCancelModal from '@ekyc/components/ConfirmCancelModal.vue';
import EkycCameraRetryOverlay from '@ekyc/components/EkycCameraRetryOverlay/EkycCameraRetryOverlay.vue';
import EkycPermissionOverlay from '@ekyc/components/EkycPermissionOverlay/EkycPermissionOverlay.vue';
import EkycRetryOverlay from '@ekyc/components/EkycRetryOverlay/EkycRetryOverlay.vue';
import { useDebug } from '@ekyc/composables/liveness/use-debug';
import { useConfirmCancel } from '@ekyc/composables/use-confirm-cancel';
import { usePopup } from '@ekyc/composables/use-popup';
import { useSwitchAppWatcher } from '@ekyc/composables/use-switch-app-watcher';
import EkycDocMediaModal from '@ekyc/items/documents/EkycDocMediaModal/EkycDocMediaModal.vue';

import TheModal from '@/components/common/TheModal.vue';
import EkycBlockingErrorOverlay from '@/components/form-input-items/ekyc/EkycBlockingErrorOverlay/EkycBlockingErrorOverlay.vue';
import EkycCardFlipOverlay from '@/components/form-input-items/ekyc/EkycCardFlipOverlay/EkycCardFlipOverlay.vue';
import EkycMaxAttemptOverlay from '@/components/form-input-items/ekyc/EkycMaxAttemptOverlay/EkycMaxAttemptOverlay.vue';
import EkycSupportedDocumentsBox from '@/components/form-input-items/ekyc/EkycSupportedDocumentsBox/EkycSupportedDocumentsBox.vue';
import { usePopupCrossDevice } from '@/composables/use-popup-cross-device';
import {
  DOCUMENT_CHOICES,
  getValidAcceptedCountryChoices,
} from '@/helpers/creator/steps/ekyc/document-utils';
import { isSuperUser } from '@/helpers/permission-utils';

import EkycBackCardWrapperNext from '../EkycBackCardWrapperNext/EkycBackCardWrapperNext.vue';
import EkycContentValidate from '../EkycContentValidate/EkycContentValidate.vue';
import { EkycProps, useCommonEkycDocumentWrapper } from '../use-common-ekyc-document-wrapper';

const LOG_PREFIX = [
  '%c[EkycDocumentNextWrapper]',
  'background: #; color: #aa80aa; border: 1px solid #aa80aa;',
];

const props = defineProps({
  ...EkycProps,
  element: {
    type: Object as () => Types.ISchemaItemEkycDocument,
    required: true,
    default: () => ({}),
  },
});

const currentStep = ref<'document' | 'backcard'>('document');
const { isConfirmCancelModalActive, onClickModalAction, removeActionHash } = useConfirmCancel(() =>
  closeHandler({ action: 'close' }),
);
const { initSwitchAppWatcher, deinitSwitchAppWatcher } = useSwitchAppWatcher();
const { showPopupCameraLockdown } = usePopup();
const { debugMode } = useDebug();

const showAllDebugButtons = ref(false);
const isMediaRecorderActive = ref<boolean>(false);
const showAutoDetectHelp = computed(
  () =>
    autoDetectDocumentType.value &&
    isMediaRecorderActive.value &&
    !overlayPopupState.value &&
    !showPopupCameraLockdown.value,
);

const crossDeviceProceedFn = () => {
  if (get(dynamicFormInstance.dataSource, [`${props.element.name}_max_attempt`], false)) {
    readyHandler();
  }
};

usePopupCrossDevice({
  afterProceedFn: crossDeviceProceedFn,
});

const headerText = computed(() => {
  return t('ekyc.document.selector_header_2');
});

const selectedCountry = computed({
  get: () =>
    get(
      dynamicFormInstance.dataSource,
      props.element.fields.country || 'ekyc_document_country',
      '',
    ) as string,
  set: val => {
    dynamicFormInstance.setFormData({
      field: props.element.fields.country || 'ekyc_document_country',
      value: val,
    });
  },
});

const { t } = useI18n();
const state = useCommonEkycDocumentWrapper({
  itemType: 'document',
  selectedCountry,
  isVNext: true,
});

const {
  selectorValue,
  previewField,
  isCameraUsedThisTime,

  value,
  setting,
  passedField,
  maxAttemptValue,
  attemptLeft,
  dynamicFormInstance,
  isSandboxEnabled,
  overlayPopupState,

  setOverlayPopupState,
  changeRightButtonToOpenCamera,
  callHook,
  setAttemptData,
  // loadEkycResult,
  checkValueChange,
  handleMaxAttempt,

  onVisible,
  handleSendEkycErrorLog,
  cameraReadyHandler,
  cameraCheckHandler,
  capturedHandler,
  // ekycUploadedHandler,
  uploadedHandler,
  closeHandler,
  callFaceCompare,
  loadingHandler,
  handleErrorType,

  forceToggleMaxAttempt,
  forceCrossDevice,

  // setFailedState,
  autoFill,
  loadFromUrl,
  resetPassFailState,

  getMediaData,

  myEntity,
  registerActionRunnerAddons,

  initAutoNextHandler,
} = state;

function retryCamera() {
  setOverlayPopupState('');
  readyHandler();
}

const latestDocumentType = ref<Types.EkycDocumentItemTypes>();

const preview = reactive<Record<Exclude<Types.EkycMediaTypes, 'liveness'>, Types.PreviewType>>({
  document: {
    url: '',
    isLoading: false,
    resultState: 'preview',
    failKeys: [],
    errorMessages: [],
    success: false,
    itemType: 'front_card',
  },
  backcard: {
    url: '',
    isLoading: false,
    resultState: 'preview',
    failKeys: [],
    errorMessages: [],
    success: false,
    itemType: 'backcard',
  },
});

const POSSIBLE_RESULT_STATE = ['preview', 'passed', 'failed_backend', 'failed_others'] as const;

// Selector & Other Childrens

const originalShowStepBar = ref<boolean>(false);
const capturedFrontCard = ref<boolean>(false);
const showSupportDocumentTypeModal = ref<boolean>(false);

const ekycComponent = templateRef('ekycComponent');

const backcardComponent = templateRef('backcardComponentRef');

const fullChoices = ref<
  (Types.Choice<Types.EkycDocumentItemTypes> & {
    icon: any;
    country_desc?: string;
    countries: Types.Choice<Types.EkycDocumentItemTypes>[];
    is_worldwide: boolean;
  })[]
>([]);

const selectedDocumentChoice = computed(() => {
  const item = fullChoices.value.find(item => item.value === selectorValue.value);
  return item;
});

const childrenSchemas = computed(() => {
  const items = { ...props.element.items };
  delete items[previewField.value];
  delete items[passedField.value];
  if (items.ekyc_document_country) {
    const countries = (selectedDocumentChoice.value?.countries || []).map(c => c.value);
    items.ekyc_document_country = {
      ...items.ekyc_document_country,
      display: {
        ...items.ekyc_document_country.display,
        description_footer: 'Select the issuing country of your ID document',
      },
      ...(countries.length > 1 ? { enum_final_filter_includes: countries } : { visible: false }),
    };
  }
  if (props.element.fields.ekyc_backcard_item) {
    delete items[props.element.fields.ekyc_backcard_item];
  }
  return items;
});

const backcardSchema = computed(() =>
  props.element.fields.ekyc_backcard_item
    ? props.element.items[props.element.fields.ekyc_backcard_item]
    : false,
);

const documentTypeFromAutoDetect = ref<Types.EkycDocumentItemTypes>();
const includeBackcard = computed(
  () => props.element.include_backcard?.[documentTypeFromAutoDetect.value || selectorValue.value],
);

// ekyc document
const extendedSettingForEachType = computed(() => {
  const urls = props.element?.urls?.[selectorValue.value] ?? {};
  return {
    ...setting.value,
    ...urls,
    ref: dynamicFormInstance.appliedFormSlug,
    selectedCountry: selectedCountry.value,
  };
});

const selectorDocumentCountry = computed(() => {
  const item =
    DOCUMENT_CHOICES.find(item => item.value === selectorValue.value) ||
    ({} as Types.Choice<Types.EkycDocumentItemTypes>);
  return {
    country: {
      value: selectedCountry.value,
      icon: selectedCountry.value
        ? getCountryIcon(selectedCountry.value)
        : 'emojione:globe-showing-americas',
    },
    document: {
      ...item,
      label: t(`ekyc.${item.value || 'document'}.label`),
    },
  };
});

const isPreviewDocumentLoading = computed(() => preview.document.isLoading);
const isPreviewBackcardLoading = computed(() => preview.backcard.isLoading);

const onTriggerNotice = () => {
  showSupportDocumentTypeModal.value = true;
};

const startInitEkycComponent = async (media: 'document' | 'backcard') => {
  if (dynamicFormInstance.inBuilder) {
    if (media === 'document') {
      ekycComponent.value.isDisplayMediaRecorder = true;
    } else {
      backcardComponent.value.ekycComponent.isDisplayMediaRecorder = true;
    }
    return;
  }

  if (media === 'document') {
    await ekycComponent.value?.startInit();
  } else {
    await backcardComponent.value?.ekycComponent?.startInit();
  }

  // Switch app watcher
  initSwitchAppWatcher(() => {
    closeHandler({ action: 'close', unfocus: true });
  });
};

// auto populate choices when call `useCommonChoiceItem`
const documentTypeKey = props.element?.fields?.document_type || 'ekyc_document_type';
const ekycDocumentTypeItem = props.element.items[documentTypeKey];
const { choices: documentTypeChoices } = useCommonChoiceItem(ekycDocumentTypeItem);
const prepareDocumentTypeChoices = () => {
  fullChoices.value = documentTypeChoices.value.map(choice => {
    // Get countries choices
    const documentType = choice.value;
    const schemaValues = props.element.accepted_countries?.[documentType] || [];
    let countryChoices = getValidAcceptedCountryChoices([documentType as string]);
    const isWorldwide = schemaValues.length === 0 || schemaValues.length === countryChoices.length;
    if (!isWorldwide) {
      countryChoices = countryChoices.filter(c => schemaValues.includes(c.value));
    }

    return {
      ...(choice as any),
      countries: countryChoices,
      is_worldwide: isWorldwide,
    };
  });
};

async function goToTakeDocument() {
  currentStep.value = 'document';
  setPreview('document', { resultState: 'preview' });
  await startInitEkycComponent('document');
}

async function goToTakeBackcard(force = false) {
  if (!value.value && !force) {
    return;
  }

  currentStep.value = 'backcard';
  setPreview('backcard', { resultState: 'preview' });
  setOverlayPopupState('card_flip');
  await Promise.all([startInitEkycComponent('backcard'), promiseTimeout(2_000)]);
  setOverlayPopupState('');
  backcardComponent.value?.ekycComponent?.goToCameraState();
}

function goToPreviewPage() {
  console.log('goToPreviewPage');
  ekycComponent.value?.stopPreview?.();
  ekycComponent.value?.closeVideoCamera?.();
  backcardComponent.value?.ekycComponent?.stopPreview?.();
  backcardComponent.value?.ekycComponent?.closeVideoCamera?.();
  removeActionHash();
  deinitSwitchAppWatcher();

  isCameraUsedThisTime.value = true;

  changeButtonToFullWidth();
}

function changeButtonToFullWidth() {
  dynamicFormInstance.formControl.rightButton.width = 'full';
}

const prefillSuccessDocumentPreview = () => {
  const document = getMediaData('document');

  if (document?.urls?.length > 0) {
    isCameraUsedThisTime.value = true;

    setPreview('document', { url: document.urls[0], success: true, isLoading: false });
    capturedFrontCard.value = true;
    dynamicFormInstance.formControl.resetRightButtonToNormal();

    changeButtonToFullWidth();
  }
};

async function loadEkycResult() {
  const result = await loadFromUrl({ loadStatus: true, loadPreview: true });

  if (result.document_type) {
    documentTypeFromAutoDetect.value = result.document_type;
  }

  prefillSuccessDocumentPreview();
  checkValueChange();

  if (!includeBackcard.value) {
    removeActionHash();
  }

  const hasBackcard =
    props.element.fields.ekyc_backcard_item &&
    props.element.items[props.element.fields.ekyc_backcard_item];

  if (hasBackcard && currentStep.value === 'document') {
    return true;
  }

  callHook('loaded');
  return false;
}

function checkCanGoToCamera() {
  /** Both selected, then go to camera */
  const documentType = selectorValue.value;
  const hasBothValues = !!selectedCountry.value && !!documentType;
  const hasAutoDetectDocumentType = autoDetectDocumentType.value;
  const hasAutoDetectCountry = props.element.auto_detect_country?.[documentType];
  if (hasBothValues || hasAutoDetectDocumentType || hasAutoDetectCountry) {
    if (documentType !== latestDocumentType.value && latestDocumentType.value) {
      dynamicFormInstance.unsetFormData({ field: 'ekyc_document' });
      dynamicFormInstance.unsetFormData({ field: 'ekyc_backcard' });
    }
    ekycComponent.value?.goToCameraState();
  }
}

function handleMaxAttemptNext() {
  // NOTE: maxAttemptValue ignores passed attempts; use numeric check but only for media not yet passed
  // Determine whether each media still needs action
  const failFront = !value.value;
  const noAttemptFront = attemptLeft.value <= 0;
  const isMaxAttemptFront = failFront && noAttemptFront;

  let isMaxAttemptBack = false;
  if (includeBackcard.value) {
    const backcardFieldKey = props.element.fields.ekyc_backcard_item;
    const failBack = !(backcardFieldKey && get(dynamicFormInstance.dataSource, backcardFieldKey));
    const noAttemptBack = backcardComponent.value?.attemptLeft <= 0;
    isMaxAttemptBack = failBack && noAttemptBack;
  }

  const isMaxAttempt = isMaxAttemptFront || isMaxAttemptBack;
  const allowMaxAttemptPass = props.element.max_attempt_warning?.allow_max_attempt_pass;
  console.log('isMaxAttemptFront: ', isMaxAttemptFront);
  console.log('isMaxAttemptBack: ', isMaxAttemptBack);
  console.log('allowMaxAttemptPass: ', allowMaxAttemptPass);

  if (isMaxAttempt) {
    if (allowMaxAttemptPass) {
      setOverlayPopupState('loading');
      handleMaxAttempt();
    } else {
      setOverlayPopupState('max_attempt');
    }
    return true;
  }

  return false;
}

function modifyCameraVideoBySchema() {
  let rotateFinal = null;
  const rotateValue = +(props.element.camera_rotate_value ?? 0);
  const rotateField = props.element.camera_rotate_field;
  const rotateAnswer = rotateField ? +get(dynamicFormInstance.dataSource, rotateField) : null;
  if (isFinite(rotateValue)) {
    rotateFinal += rotateValue;
  }
  if (isFinite(rotateAnswer)) {
    rotateFinal += rotateAnswer;
  }
  if (isFinite(rotateFinal)) {
    ekycComponent.value?.rotateCamera(rotateFinal);
  }

  let zoomFinal = null;
  const zoomValue = +(props.element.camera_zoom_value ?? 1);
  const zoomField = props.element.camera_zoom_field;
  const zoomAnswer = zoomField ? +get(dynamicFormInstance.dataSource, zoomField) : null;
  if (isFinite(zoomValue)) {
    zoomFinal += zoomValue;
  }
  if (isFinite(zoomAnswer)) {
    zoomFinal += zoomAnswer;
  }
  if (isFinite(zoomFinal)) {
    ekycComponent.value?.zoomCamera(zoomFinal);
  }

  const flipValue = props.element.camera_flip_value;
  const flipField = props.element.camera_flip_field;
  const flipAnswer: boolean = flipField ? get(dynamicFormInstance.dataSource, flipField) : false;
  if (flipValue || flipAnswer) {
    ekycComponent.value?.flipCamera(flipValue || flipAnswer);
  }

  console.log(
    `Modify camera by schema: 
      rotate=${rotateValue}+${rotateAnswer}=${rotateFinal}
      zoom=${zoomValue}+${zoomAnswer}=${zoomFinal}
      flip=${flipValue}+${flipAnswer}=${flipValue || flipAnswer}`,
  );
}

async function readyHandler() {
  dynamicFormInstance.formControl.setItemReady(props.element.name);

  if (isSandboxEnabled.value) {
    removeActionHash();
    changeRightButtonToOpenCamera();
    return;
  }

  if (handleMaxAttemptNext()) {
    return;
  }

  if (value.value) {
    setPreview('document', { isLoading: true });
    await loadEkycResult();

    if (!checkShouldGoBackcard()) {
      return;
    }
  }

  resolveDocumentTypeAndCountry();

  await startInitEkycComponent('document');

  modifyCameraVideoBySchema();
}

function checkShouldGoBackcard() {
  const isBackcardPassed = props.element.fields.ekyc_backcard_item
    ? !!get(dynamicFormInstance.dataSource, props.element.fields.ekyc_backcard_item)
    : false;

  return includeBackcard.value && currentStep.value === 'document' && !isBackcardPassed;
}

function documentCapturedHandler(e: any) {
  capturedFrontCard.value = false;
  capturedHandler(e);

  // Call save to save ekyc_document_type
  dynamicFormInstance.formControl.save({ saveIfValidationFailed: true, showErrors: false });

  // Next: Back Card
  if (checkShouldGoBackcard()) {
    goToTakeBackcard(true);
    capturedFrontCard.value = true;
  } else {
    goToPreviewPage();
  }
}

function backcardCapturedHandler(e: any) {
  backcardComponent.value?.capturedHandler(e);
  goToPreviewPage();
}

function processDocumentResult(media: Types.EkycMediaTypes, e: Types.EkycDocumentApiResult) {
  console.log('processDocumentResult');
  const passResult = e.data.result;

  const failKeys = new Set<Types.EkycDocumentValidationTypes>([]);
  let resultState: Types.EkycResultState = 'preview';

  let passAll = true;
  const elementName =
    media === 'backcard' ? props.element.fields.ekyc_backcard_item : props.element.name;
  Object.keys(passResult).forEach((key: Types.EkycDocumentValidationTypes) => {
    const pass = passResult[key].status;
    dynamicFormInstance.setInputData({
      field: `${elementName}_${key}_passed`,
      value: pass,
    });

    if (!pass) {
      console.warn(`${key} not passed`);
      failKeys.add(key);
      passAll = false;
      resultState = 'failed_backend';
    }
  });

  setPreview(media, {
    failKeys: [...failKeys],
    resultState,
  });

  return passAll;
}

function documentUploadedHandler(media: Types.EkycMediaTypes, e: Types.EkycDocumentApiResult) {
  console.log('documentUploadedHandler', media, e);

  /* auto-detect document_type */
  if (e.document_type) {
    documentTypeFromAutoDetect.value = e.document_type;
  }

  const isDocument = media === 'document';
  const callError = isDocument ? errorHandler : backcardComponent.value?.errorHandler;
  const callUploaded = isDocument ? uploadedHandler : backcardComponent.value?.ekycUploadedHandler;
  const callAutofill = isDocument ? autoFill : backcardComponent.value?.autoFill;

  const passAll = processDocumentResult(media, e);

  /* Failed */
  if (!passAll) {
    callError?.(media as any, e as any);
    checkButtonRetake();
    return;
  }

  /* Autofill */
  if (e.ocr) {
    try {
      callAutofill?.(e.ocr);
    } catch (err) {
      console.error('Filling ocr error', err);
    }
  }

  callUploaded?.(e);

  setPreview(media, { success: true });

  changeButtonToFullWidth();
  if (includeBackcard.value) {
    if (preview.document.success && preview.backcard.success) {
      dynamicFormInstance.formControl.resetRightButtonToNormal();
    }
  } else {
    dynamicFormInstance.formControl.resetRightButtonToNormal();
  }

  if (includeBackcard.value && capturedFrontCard.value) {
    // Do nothing
  } else {
    /* Set answer for auto detect document ocr */
    if (autoDetectDocumentType.value) {
      if (documentTypeFromAutoDetect.value) {
        dynamicFormInstance.setFormData({
          field: props.element.fields.document_type,
          value: documentTypeFromAutoDetect.value,
        });
      }
    }
    goToPreviewPage();
  }
}

function errorHandler(media: Types.EkycMediaTypes, errorBody: Types.EkycRecordErrorPayload = null) {
  console.log(...LOG_PREFIX, 'ekyc_error', JSON.stringify(errorBody, null, 2));
  callHook('error');
  callFaceCompare(errorBody);
  loadingHandler(false); // Fix a case where item hidden but still loading
  dynamicFormInstance.setInputData({
    field: `${props.element.name}_failed`,
    value: true,
  });

  let resultState: Types.EkycResultState = preview[media].resultState;

  const errorData = get(errorBody, 'error.data', errorBody);
  setAttemptData(errorData);

  if (errorBody.code === 'error_open_camera_timeout') {
    setOverlayPopupState('camera_retry');
  } else if (errorBody.from === 'init') {
    setOverlayPopupState('camera_permission');
  } else if (errorBody.error_type === 'blocking') {
    setOverlayPopupState('blocking_error');
  } else {
    handleMaxAttemptNext();
  }

  // Already processed
  if (resultState === 'failed_backend') {
    return;
  }

  // If not in fail error from backend, set to other error
  const errorMessages = handleErrorType(errorBody);

  handleSendEkycErrorLog(errorBody);

  setPreview(media, {
    failKeys: [],
    errorMessages,
    resultState: 'failed_others',
  });
}

const autoDetectDocumentType = computed(() => {
  return props.element.auto_detect_document_type === true;
});

const canGoBackSelector = computed(() => {
  const documentType = selectorValue.value;
  const hasAutoDetectCountry = props.element.auto_detect_country?.[documentType];

  const countries = selectedDocumentChoice.value?.countries || [];
  const hasMultiple =
    (countries.length > 1 && !hasAutoDetectCountry) || fullChoices.value.length > 1;
  const allDisabled = Object.values(childrenSchemas.value).every(schema => schema.props?.disabled);
  return hasMultiple && !allDisabled;
});

const goBackToSelector = () => {
  latestDocumentType.value = selectorValue.value;
  selectorValue.value = undefined;
  ekycComponent.value.deselectDocumentType();
};

const setPreview = (media: Types.EkycMediaTypes, payload: Partial<Types.PreviewType>) => {
  payload.itemType =
    media === 'backcard'
      ? media
      : selectorValue.value || documentTypeFromAutoDetect.value || 'front_card';
  if (preview[media]) {
    Object.assign(preview[media], { ...payload });
  }
  checkButtonRetake();
};

function resolveDocumentTypeAndCountry() {
  prepareDocumentTypeChoices();
  autoSelectDocumentType();
  autoSelectCountry();
  checkCanGoToCamera();
}

function restart() {
  try {
    resetPassFailState();
    callHook('restart');
    setOverlayPopupState('');
    resolveDocumentTypeAndCountry();

    // Reset type to disable invalid backcard on start
    documentTypeFromAutoDetect.value = undefined;

    const foundIdFrontToRetake = ['failed_backend', 'failed_others'].includes(
      preview.document.resultState,
    );

    if (foundIdFrontToRetake) {
      goToTakeDocument();
    } else {
      goToTakeBackcard();
    }
    return true;
  } catch (err) {
    console.warn('Restart error', err);
    return false;
  }
}

function checkButtonRetake() {
  const needToRetakeIdBack =
    ['failed_backend', 'failed_others'].includes(preview.backcard.resultState) &&
    includeBackcard.value;
  const foundIdFrontToRetake = ['failed_backend', 'failed_others'].includes(
    preview.document.resultState,
  );

  if (needToRetakeIdBack || foundIdFrontToRetake) {
    dynamicFormInstance.formControl.rightButton.label = (() => {
      if (foundIdFrontToRetake && needToRetakeIdBack) {
        return t('ekyc.selector.retake_both_btn');
      }
      if (foundIdFrontToRetake) {
        const itemType = selectorValue.value || documentTypeFromAutoDetect.value || 'front_card';
        return itemType ? t(`ekyc.${itemType}.retake`) : t('ekyc.selector.retake_btn');
      }
      if (needToRetakeIdBack) {
        return t('ekyc.backcard.retake');
      }
    })() as string;

    if (!preview.backcard.isLoading && !preview.document.isLoading) {
      dynamicFormInstance.formControl.rightButton.disabled = false;
    }
    dynamicFormInstance.formControl.rightButton.onClick = async () => {
      const success = restart();
      if (!success) {
        dynamicFormInstance.formControl.resetLeftButtonToNormal();
      }
    };
  } else {
    dynamicFormInstance.formControl.resetRightButtonToNormal();
    changeButtonToFullWidth();
  }

  // AUTO NEXT if enabled when success and can continue to next step
  const documentSuccess = preview.document.success;
  const backcardSuccess =
    !includeBackcard.value || (includeBackcard.value && preview.backcard.success);
  if (!needToRetakeIdBack && !foundIdFrontToRetake && documentSuccess && backcardSuccess) {
    initAutoNextHandler();
  }
}

function autoSelectDocumentType() {
  if (autoDetectDocumentType.value) {
    return;
  }

  if (fullChoices.value.length !== 1) {
    return;
  }

  selectorValue.value = fullChoices.value[0].value;
}

function autoSelectCountry() {
  // Auto-select if only one country
  const countries = (selectedDocumentChoice.value?.countries || []).map(c => c.value);
  if (countries.length === 1) {
    selectedCountry.value = countries[0];
  } else if (selectedCountry.value && !countries.includes(selectedCountry.value)) {
    selectedCountry.value = '';
  }
}

function onCloseSupportedDocuments() {
  ekycComponent.value?.goToCameraState();
}

watch(
  [() => isPreviewDocumentLoading.value, () => isPreviewBackcardLoading.value],
  ([loadingDocument, loadingBackcard]) => {
    dynamicFormInstance.formControl.rightButton.disabled = loadingDocument || loadingBackcard;
  },
);

watch(
  () => ekycComponent.value?.isDisplayMediaRecorder,
  val => {
    isMediaRecorderActive.value = !!val;
  },
);

watch(showAutoDetectHelp, val => {
  if (!val) {
    showSupportDocumentTypeModal.value = false;
  }
});

watchDebounced(
  [selectedCountry, selectorValue],
  () => {
    resolveDocumentTypeAndCountry();
  },
  { debounce: 100 } as WatchDebouncedOptions<any>,
);

const restoreShowStepBarSetting = () => {
  dynamicFormInstance.stepbar.stepbarConfig.show_stepbar = originalShowStepBar.value;
};

onUnmounted(() => {
  restoreShowStepBarSetting();
});

onMounted(() => {
  originalShowStepBar.value = !!dynamicFormInstance?.stepbar?.stepbarConfig?.show_stepbar;
  dynamicFormInstance.stepbar.stepbarConfig.show_stepbar = false;
  dynamicFormInstance.currentSection.hide_label = true;
  changeButtonToFullWidth();

  if (autoDetectDocumentType.value && !selectorValue.value) {
    dynamicFormInstance.setInputData({
      field: props.element.fields.document_type,
      value: 'auto',
    });
  }
});

onBeforeUnmount(() => {
  dynamicFormInstance.formControl.rightButton.resetToNormal();
  ekycComponent.value?.stopPreview?.({ tryRequestAndStopExternal: false });
  deinitSwitchAppWatcher();
});

registerActionRunnerAddons([
  {
    entity: myEntity,
    methodMap: { setOverlayPopupState },
  },
]);

defineExpose({
  ...state,
  value,
  overlayPopupState,
  // showSelector,
});
</script>

<style scoped lang="scss">
.support-document-trigger {
  @apply fixed top-[25px] right-[25px] z-[100000] h-[22px] w-[22px] text-white cursor-pointer;
}

.support-document-modal {
  :deep(.modal-card) {
    margin: 24px;
    width: calc(100% - 48px);
  }
  :deep(.modal-card-head) {
    padding: 18px 24px;
    border-radius: 16px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;

    .modal-card-title > span {
      font-weight: 500 !important;
      font-size: 14px !important;
      line-height: 20px !important;
    }

    > .delete::before,
    .delete::after {
      background-color: var(--app-button-background-color);
    }
  }
  :deep(.modal-card-body) {
    max-height: 50dvh;
    border-radius: 16px;
    padding: 24px;
  }
}

body:has(#camera-doc-media-modal) .support-document-modal :deep(.modal-card-body) {
  overflow: auto !important;
}

.selector-trigger {
  @apply flex flex-row items-center p-3 bg-white rounded-[12px] min-w-[248px] justify-between;

  .vertical-divider {
    background-color: rgba(42, 48, 73, 0.2);
    height: 24px;
    width: 1px;
  }
  .dropdown-icon {
    color: var(--app-primary-color);
  }
}

:deep(.ok-dy__input_radio_static_icon) .radio-item {
  background-color: white;
  color: var(--color-text, #2a3049);
}
</style>
