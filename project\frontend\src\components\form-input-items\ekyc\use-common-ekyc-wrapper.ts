import { promiseTimeout, until } from '@vueuse/core';
import debounce from 'lodash/debounce';
import get from 'lodash/get';
import mapValues from 'lodash/mapValues';
import merge from 'lodash/merge';
import { storeToRefs } from 'pinia';
import type { ExtractPropTypes } from 'vue';
import { useI18n } from 'vue-i18n-composable';

import { useSchemaConfigFormSettings } from '@core/composables/dynamic-form-settings/use-schema-config-form-settings';
import { Props, useCommonFormItem } from '@core/composables/use-common-form-item';
import { useDynamicFormApp } from '@core/composables/use-dynamic-form-app';
import FileInfo from '@core/helpers/form-data/FileInfo';
import i18n from '@core/plugins/i18n';

import { useDebug } from '@ekyc/composables/liveness/use-debug';
import { setCameraBlockRules } from '@ekyc/composables/use-camera';
import { EKYC_DOCUMENT_API_TYPES } from '@ekyc/helpers/document-types';
import type EkycDocFrontCard from '@ekyc/items/documents/EkycDocFrontCard.vue';
import type EkycLiveness from '@ekyc/items/liveness/EkycLiveness.vue';
import { useEkycStore } from '@ekyc/store/modules/ekyc';

import { isSuperUser } from '@/helpers/permission-utils';

import { useEkycOverlayPopup } from './use-ekyc-overlay-popup';

type EkycComponentType = InstanceType<typeof EkycLiveness | typeof EkycDocFrontCard>;

const LOG_PREFIX = [
  '%c[use-common-ekyc-wrapper]',
  'background: #; color: #aa80aa; border: 1px solid #aa80aa;',
];

const DEFAULT_AUTO_NEXT_DELAY = 1_000;

export const EkycProps = {
  ...Props,
  element: {
    type: Object as () => Types.ISchemaItemEkyc,
    required: true,
    default: () => ({}),
  },
};

/* eslint-enable */

const MEDIA_MAP: Record<Types.EkycItemTypes | Types.EkycMediaTypes, Types.EkycMediaTypes> = {
  liveness: 'liveness',
  document: 'document',
  backcard: 'backcard',
  ...EKYC_DOCUMENT_API_TYPES.reduce(
    (acc, cur) => {
      acc[cur] = 'document';
      return acc;
    },
    {} as Record<Types.EkycDocumentItemTypes, Types.EkycMediaTypes>,
  ),
};

export const useCommonEkycWrapper = (itemType: keyof typeof MEDIA_MAP, isVNext = false) => {
  const context = getCurrentInstance();

  const props = context?.proxy.$props as ExtractPropTypes<typeof EkycProps>;

  const MEDIA = MEDIA_MAP[itemType];

  const value = ref<string | true | null>(null);
  const lastValue = ref<string | true | null>(null);
  const localFile = ref<FileInfo>();
  const isCameraUsedThisTime = ref(false);

  const errorMessages = ref<string[]>([]);

  const isSandboxEnabled = ref(false);

  const { overlayPopupState, setOverlayPopupState } = useEkycOverlayPopup();

  const { t, locale } = useI18n();
  const ekycStore = useEkycStore();
  const { resultState } = storeToRefs(ekycStore);
  const { allowDebugMode } = useDebug();
  const { http } = useDynamicFormApp();
  const {
    dynamicFormApp,
    dynamicFormInstance,
    myEntity,
    callHook,
    setFlag,
    unsetFlag,
    registerActionRunnerAddons,
    ...common
  } = useCommonFormItem({
    value,
    saveMode: 'inputData',
  });

  const previewField = computed<string>(() => get(props.element, ['fields', 'preview']));
  const previewSchema = computed<Types.ISchemaItem>(() =>
    get(props.element, ['items', previewField.value]),
  );
  const passedField = computed<string>(() => get(props.element, ['fields', 'passed']));
  const passedSchema = computed<Types.ISchemaItem>(() =>
    get(props.element, ['items', passedField.value]),
  );
  const cameraSideField = computed<string>(() => get(props.element, ['fields', 'camera_side']));
  const cameraSideValue = computed<string>(() =>
    get(dynamicFormInstance.dataSource, [cameraSideField.value]),
  );
  const maxAttemptValue = computed<boolean>({
    get: () => get(dynamicFormInstance.dataSource, [`${props.element.name}_max_attempt`], false),
    set: val => {
      dynamicFormInstance.setInputData({
        field: `${props.element.name}_max_attempt`,
        value: val,
      });
    },
  });
  const attemptLeft = computed<number>({
    get: () => get(dynamicFormInstance.dataSource, [`${props.element.name}_attempt_left`], 1),
    set: val => {
      dynamicFormInstance.setInputData({
        field: `${props.element.name}_attempt_left`,
        value: val,
      });
    },
  });
  const disabledDefaultPreview = computed<Types.ISchemaItem>(() =>
    get(props.element, ['display', 'disabled_default_preview'], false),
  );

  const setting = computed(() => {
    const base: Partial<Types.BaseEkycComponentSetting> = {
      ref: dynamicFormInstance.appliedFormSlug,
      upload_url: props.element.upload_url,
      log_url: props.element.log_url,
      check_quality_url: props.element.check_quality_url,
      dummy: dynamicFormInstance.inBuilder,
      additional_constraints: merge(
        {},
        props.element.additional_constraints,
        cameraSideValue.value
          ? {
              video: {
                facingMode: cameraSideValue.value,
              },
            }
          : {},
      ),
    };
    merge(base, props.element);
    return base;
  });

  const failKeys = ref<Types.EkycDocumentValidationTypes[]>([]);

  function setFailedState(type: Types.EkycDocumentValidationTypes) {
    if (!failKeys.value.includes(type)) {
      failKeys.value.push(type);
    }
    if (failKeys.value.length > 0) {
      resultState.value = 'failed_backend';
    }
  }

  function resetPassFailState() {
    resultState.value = 'preview';
    dynamicFormInstance.stepbar.stepbarConfig.show_title = true;
    dynamicFormInstance.formControl.rightButton.label = () =>
      t('ekyc.selector.camera_btn') as string;
    failKeys.value = [];
  }

  async function loadFromUrl({ loadStatus = true, loadPreview = true }) {
    if (!value.value || typeof value.value !== 'string') {
      return null;
    }

    if (loadPreview) {
      // Clear old one
      ekycStore.setBlob(MEDIA, { urls: [] });
    }

    const res = await http.get<Types.ApplicationEkycReportResult>(value.value);

    if (loadStatus) {
      const status = res.data?.status;
      if (!status) {
        value.value = null;
      }
    }

    if (loadPreview) {
      const img = res.data?.preview;
      if (img) {
        // Load new one
        ekycStore.setBlob(MEDIA, { urls: [img], progress: 100 });
      }
    }

    return res.data;
  }

  dynamicFormInstance.formControl.beforeNextFunctions[props.element.name] = () => {
    if (autoNext) {
      autoNext?.cancel();
    }
  };

  let autoNext = debounce(() => {
    dynamicFormInstance.formControl.next();
  }, DEFAULT_AUTO_NEXT_DELAY);

  function handleMaxAttempt() {
    callHook('max_attempt');
    resultState.value = 'failed_others';
    errorMessages.value = ['ekyc.recorder.error_card_max_attempt'];
    dynamicFormInstance.formControl.resetLeftButtonToNormal();
    dynamicFormInstance.formControl.resetRightButtonToNormal();
    if (props.element.max_attempt_warning?.allow_max_attempt_pass) {
      console.log('handleMaxAttempt: allow pass = AUTO NEXT');
      autoNext();
    } else {
      console.log('handleMaxAttempt: MAX ATTEMPT dead end');
      dynamicFormInstance.formControl.leftButton.hidden = true;
      dynamicFormInstance.formControl.rightButton.hidden = true;
    }
  }

  function setAttemptData(data: {
    attempt_count?: number;
    attempt_left?: number;
    max_attempt?: boolean;
  }) {
    const attemptCount = get(data, 'attempt_count');
    const attemptleft = get(data, 'attempt_left');
    const isMaxAttempt = get(data, 'max_attempt');
    if (typeof attemptCount === 'number') {
      dynamicFormInstance.setInputData({
        field: `${props.element.name}_attempt_count`,
        value: attemptCount,
      });
      dynamicFormInstance.setInputData({
        field: `${props.element.name}_attempt_left`,
        value: attemptleft,
      });
    }
    if (!value.value && isMaxAttempt) {
      maxAttemptValue.value = true;
      handleMaxAttempt();
    }
  }

  function callFaceCompare(e: any) {
    // If ensure facecompare, facecompare already called in upload
    // If not ensure facecompare, Send compare api in background instead
    if (!props.element.ensure_facecompare && e.compare_url) {
      console.log('calling facecompare', e.compare_url);
      http.post(e.compare_url);
    }
  }

  async function callOpenVideoCamera() {
    await promiseTimeout(10);
    if (isSandboxEnabled.value) {
      dynamicFormInstance.formControl.clickRight();
      return;
    }
    callHook('opened');
    await until(() => !!context?.proxy?.$refs?.ekycComponent).toBe(true, {
      timeout: 500,
      throwOnTimeout: false,
    });

    const ekycComponent = context?.proxy.$refs.ekycComponent as unknown as EkycComponentType;
    ekycComponent?.beforeCameraRequestCallbacks?.push(async () => {
      if (!isCameraUsedThisTime.value) {
        dynamicFormInstance.formControl.save({ saveIfValidationFailed: true, showErrors: false });
      }
      return true;
    });
    isCameraUsedThisTime.value = await ekycComponent?.openVideoCamera?.();
    if (isCameraUsedThisTime.value) {
      callHook('can_open_camera', {}, true);
      unsetFlag('cannot_open_camera');
    } else {
      callHook('cannot_open_camera', {}, true);
      unsetFlag('can_open_camera');
    }
  }

  function changeRightButtonToOpenCamera() {
    dynamicFormInstance.formControl.resetLeftButtonToNormal();

    if (isSandboxEnabled.value) {
      console.log('changeRightButtonToOpenCamera: SANDBOX ENABLED');
      dynamicFormInstance.formControl.rightButton.disabled = false;
      dynamicFormInstance.formControl.rightButton.label = () => 'Skip';
      dynamicFormInstance.formControl.rightButton.onClick = async () => {
        dynamicFormInstance.formControl.next(true, {
          saveIfValidationFailed: true,
          showErrors: false,
        });
      };
    } else {
      dynamicFormInstance.formControl.rightButton.label = () =>
        t('ekyc.selector.camera_btn') as string;
      dynamicFormInstance.formControl.rightButton.onClick = async () => {
        try {
          await callOpenVideoCamera();
        } catch (e) {
          console.error(e);
          dynamicFormInstance.formControl.resetRightButtonToNormal();
        }
      };
    }
  }

  function checkValueChange() {
    resultState.value = 'hidden';

    // MAX ATTEMPT
    if (maxAttemptValue.value) {
      handleMaxAttempt();
      return;
    }

    // No value
    if (!value.value) {
      console.log('checkValueChange:', 'No value');
      console.log('isVNext:', isVNext);
      resultState.value = 'preview';
      if (isVNext) {
        if (!dynamicFormInstance.inBuilder) {
          callOpenVideoCamera();
        } else {
          console.log('[IN BUILDER]: Not request camera');
        }
      } else {
        changeRightButtonToOpenCamera();
      }
      return;
    }

    // When clicking previous in SANDBOX enter this step, will try to skip to adj. step
    if (isSandboxEnabled.value) {
      console.log('checkValueChange:', 'SANDBOX reset');
      resultState.value = 'preview';
      changeRightButtonToOpenCamera();
      return;
    }

    // *** ==== All condition after this has truthy "value.value" ==== *

    dynamicFormInstance.stepbar.stepbarConfig.show_title = false;
    dynamicFormInstance.formControl.resetRightButtonToNormal();
    dynamicFormInstance.formControl.resetLeftButtonToNormal();

    if (isVNext) {
      dynamicFormInstance.formControl.rightButton.width = 'full';
    }

    callHook('finished');

    // * Just entered this page
    if (!isCameraUsedThisTime.value) {
      // Document Step Always retry
      if (attemptLeft.value) {
        callHook('restart');
        callOpenVideoCamera();
        dynamicFormInstance.formControl.resetRightButtonToDefault();
        return;
      }
      // Auto-next if no attempt left
      if (!attemptLeft.value) {
        console.log('checkValueChange:', 'AUTO NEXT:', 'no attempt left & just refreshed');
        autoNext();
        return;
      }
    }

    // * Just did an attempt
    else {
      // Auto-next if has config & Just did liveness in this session
      const shouldRunAutoNext = initAutoNextHandler();
      if (shouldRunAutoNext) {
        console.log('checkValueChange:', 'AUTO NEXT:', 'has config & camera used');
        return;
      }
    }

    // Old Exp: Show passed state
    resultState.value = 'passed';
    if (!['passed', 'already_passed'].includes(resultState.value)) {
      console.log('checkValueChange:', 'Has value && Old Exp');
      if (MEDIA === 'document' && !isVNext) {
        dynamicFormInstance.formControl.leftButton.label = () => t('ekyc.selector.retake_btn');
        dynamicFormInstance.formControl.leftButton.hidden = false;
        dynamicFormInstance.formControl.leftButton.onClick = async () => {
          try {
            await callOpenVideoCamera();
          } catch (e) {
            dynamicFormInstance.formControl.resetLeftButtonToNormal();
          }
        };
      }
    }
  }

  function initAutoNextHandler(): boolean {
    const autoNextValue =
      typeof props.element.auto_next === 'boolean'
        ? props.element.auto_next
        : props.element.auto_next?.value;
    const autoNextDelay =
      typeof props.element.auto_next === 'boolean'
        ? DEFAULT_AUTO_NEXT_DELAY
        : props.element.auto_next?.delay;
    const shouldRunAutoNext = autoNextValue && isCameraUsedThisTime.value;

    if (shouldRunAutoNext) {
      if (autoNextDelay >= 0) {
        autoNext?.cancel();
        autoNext = debounce(() => {
          dynamicFormInstance.formControl.next();
        }, autoNextDelay);
        autoNext();
      } else {
        autoNext();
      }
    }

    return shouldRunAutoNext;
  }

  async function loadEkycResult() {
    ekycStore.mediaUploadStart({ media: MEDIA, progress: 100 });
    await loadFromUrl({ loadStatus: true, loadPreview: true });
    checkValueChange();
    ekycStore.mediaUploadSuccess({ media: MEDIA });
    callHook('loaded');
  }

  async function readyHandler() {
    await loadEkycResult();
    dynamicFormInstance.formControl.setItemReady(props.element.name);
  }

  function loadingHandler(loading: boolean) {
    setTimeout(() => {
      dynamicFormInstance.formControl.leftButton.loading = loading;
      dynamicFormInstance.formControl.rightButton.loading = loading;
    }, 10);
  }

  function handleSendEkycErrorLog(
    errorBody: Types.EkycRecordErrorPayload | Types.EkycRecordFailPayload = null,
  ) {
    try {
      delete errorBody.error.actionLogs;
      delete errorBody.error.processLogs;
      if (MEDIA === 'liveness') {
        delete errorBody.logs;
      }
    } catch {}

    if (['preview', 'failed_others'].includes(resultState.value)) {
      dynamicFormInstance.formData.sendLog({
        action: `frontend_ekyc_${MEDIA}`,
        is_fail: true,
        message: t(errorMessages.value[0]) as string,
        detail: errorBody,
      });
    }
  }

  function handleErrorType(errorBody: Types.EkycRecordErrorPayload = null) {
    const errorData = get(errorBody, 'error.data', errorBody);
    const errorType = get(errorData, 'error_type');

    let errorMessages: string[] = [];
    if (Array.isArray(errorBody)) {
      errorMessages = errorBody;
    } else if (errorType === 'max_attempt') {
      errorMessages = ['ekyc.recorder.error_card_max_attempt'];
    } else if (errorType === 'blocking') {
      errorMessages = ['ekyc.recorder.error_blocking'];
    } else if (errorType === 'session_invalid') {
      errorMessages = ['ekyc.recorder.error_session_invalid'];
    } else if (errorType === 'face_compare_failed') {
      errorMessages = ['ekyc.recorder.error_ensure_face_compare_failed'];
      dynamicFormInstance.setInputData({
        field: `${props.element.name}_ensure_face_compare_failed`,
        value: true,
      });
    } else if (get(errorBody, 'from') === 'server') {
      errorMessages = ['ekyc.recorder.error_server'];
    } else {
      errorMessages = [
        get(errorBody, 'error.message', '') ||
          get(errorBody, 'error.status', '') ||
          get(errorBody, 'error.data.error_type', '') ||
          get(errorBody, 'error', '') ||
          errorBody,
      ];
    }
    return errorMessages;
  }

  function errorHandler(errorBody: Types.EkycRecordErrorPayload = null) {
    console.log(...LOG_PREFIX, 'ekyc_error', JSON.stringify(errorBody, null, 2));
    callHook('error');
    callFaceCompare(errorBody);
    loadingHandler(false); // Fix a case where item hidden but still loading
    dynamicFormInstance.setInputData({
      field: `${props.element.name}_failed`,
      value: true,
    });
    if (lastValue.value) {
      console.log('loading old result back', lastValue.value);
      value.value = lastValue.value;
    }
    if (value.value) {
      resultState.value = 'already_passed';
      loadFromUrl({ loadStatus: true, loadPreview: true });
    } else {
      if (!['failed_backend', 'failed_others'].includes(resultState.value)) {
        resultState.value = 'failed_others';
      }
      dynamicFormInstance.formControl.rightButton.label = () =>
        t('ekyc.selector.retake_btn') as string;
    }
    const errorData = get(errorBody, 'error.data', errorBody);
    setAttemptData(errorData);

    // If not in fail error from backend, set to other error
    if (resultState.value !== 'failed_backend') {
      errorMessages.value = handleErrorType(errorBody);
    }

    handleSendEkycErrorLog(errorBody);
  }

  function capturedHandler(e: any) {
    console.log('ekyc_captured', JSON.stringify(e));
    resetPassFailState();
    callHook('captured');
    dynamicFormInstance.stepbar.stepbarConfig.show_title = false;
    dynamicFormInstance.errors.remove(props.element.name);
    lastValue.value = value.value;
    value.value = null;
  }

  function uploadedHandler(e: Types.EkycUploadApiResult) {
    console.log('ekyc_uploaded', e);
    callHook('uploaded');
    // If ensure facecompare, facecompare already called in upload
    // If not ensure facecompare, Send compare api in background instead
    callFaceCompare(e);
    const result = get(e, ['result_url'], true);
    value.value = result;
    dynamicFormInstance.setInputData({
      field: props.element.name,
      value: result,
    });
    dynamicFormInstance.setInputData({
      field: `${props.element.name}_failed`,
      value: false,
    });

    // If not success, set value to null
    if (e.code !== 200) {
      value.value = null;
    }

    // Load preview
    const img = e?.data?.image_url?.img;
    if (img) {
      ekycStore.setBlob(MEDIA, { urls: [img] });
    }

    setAttemptData(e);
  }

  function closeHandler(e: Types.EkycRecordClosePayload) {
    resetPassFailState();
    callFaceCompare(e);
    setAttemptData(e);
    callHook('closed');

    const UNFOCUS_UNCHANGEABLE_STATES = [
      'max_attempt',
      'loading',
      'card_flip',
      'timeout',
      'fail',
      'blocking_error',
    ] as (typeof overlayPopupState.value)[];
    if (e.unfocus && !UNFOCUS_UNCHANGEABLE_STATES.includes(overlayPopupState.value)) {
      setOverlayPopupState('unfocus_app');
    }
  }

  // Debug functions
  function forceToggleMaxAttempt() {
    if (maxAttemptValue.value) {
      maxAttemptValue.value = undefined;
    } else {
      maxAttemptValue.value = true;
    }
  }

  function forceCrossDevice() {
    if (maxAttemptValue.value) {
      dynamicFormInstance.setInputData({ field: '_cross_device', value: true });
    } else {
      dynamicFormInstance.setInputData({ field: '_cross_device', value: undefined });
    }
  }

  function forceEkycResultState(status: string) {
    resultState.value = status as Types.EkycResultState;
  }

  watch(
    () => props.element.override_messages,
    (val: Record<string, string>) => {
      let message = val;
      if (typeof props.element.override_messages === 'string') {
        try {
          message = JSON.parse(props.element.override_messages);
          message = JSON.parse(JSON.stringify(message));
        } catch {
          message = {};
        }
      }
      i18n.mergeLocaleMessage(locale.value, message);
    },
    { immediate: true, deep: true },
  );

  function unmountThisComponent() {
    dynamicFormInstance.errors.remove(props.element.name);
    dynamicFormInstance.formControl.resetLeftButtonToNormal();
    dynamicFormInstance.formControl.resetRightButtonToNormal();
    dynamicFormInstance.stepbar.stepbarConfig.show_title = true;
    isCameraUsedThisTime.value = false;
    unsetFlag('can_open_camera');
    unsetFlag('cannot_open_camera');
    console.log('eKYC wrapper unmounted');
  }

  const visibledOnce = ref(false);

  function onVisible(visible: boolean) {
    if (visible) {
      visibledOnce.value = true;
      allowDebugMode.value = isSuperUser();
      resetPassFailState();

      watch(value, () => {
        if (!isVNext) {
          checkValueChange();
        }
      });

      // Load FormSetting
      const { schemaConfigSetting } = useSchemaConfigFormSettings(
        computed(() => dynamicFormInstance.formSlug),
      );
      isSandboxEnabled.value = schemaConfigSetting.value?.configs?.enable_sandbox_mode ?? false;
      setCameraBlockRules(schemaConfigSetting.value?.configs?.camera_blocking_rules ?? {});

      return;
    }

    if (visibledOnce.value) {
      unmountThisComponent();
    }
  }

  onBeforeUnmount(() => {
    unmountThisComponent();
  });

  registerActionRunnerAddons([
    {
      entity: myEntity,
      methodMap: { callOpenVideoCamera },
    },
  ]);

  return {
    ...common,
    dynamicFormInstance,

    MEDIA,
    value,
    lastValue,
    localFile,

    failKeys,
    resultState,
    errorMessages,

    previewField,
    previewSchema,
    passedField,
    passedSchema,
    maxAttemptValue,
    attemptLeft,
    disabledDefaultPreview,
    isCameraUsedThisTime,
    isSandboxEnabled,

    overlayPopupState,
    setOverlayPopupState,

    locale,
    setting,

    myEntity,
    callHook,
    registerActionRunnerAddons,

    callFaceCompare,
    setAttemptData,
    setFlag,
    unsetFlag,
    loadFromUrl,
    handleSendEkycErrorLog,
    readyHandler,
    errorHandler,
    capturedHandler,
    uploadedHandler,
    closeHandler,
    handleErrorType,
    handleMaxAttempt,
    callOpenVideoCamera,
    checkValueChange,
    loadEkycResult,
    onVisible,
    loadingHandler,

    setFailedState,
    resetPassFailState,
    forceToggleMaxAttempt,
    forceCrossDevice,
    forceEkycResultState,
    changeRightButtonToOpenCamera,

    getMediaData: ekycStore.getMediaData,
    mediaUploadStart: ekycStore.mediaUploadStart,
    mediaUploadSuccess: ekycStore.mediaUploadSuccess,

    initAutoNextHandler,
  };
};

export default useCommonEkycWrapper;
