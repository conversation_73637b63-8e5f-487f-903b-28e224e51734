/// <reference path="./base.d.ts" />
/// <reference path="../../../../ekyc/src/types/component-setting.d.ts" />

declare module Types {
  type ISchemaItemEkyc = ISchemaItem &
    Partial<BaseEkycComponentSetting> & {
      max_attempt_warning?: Types.EkycMaxAttemptWarningSetting;
      blocking_error?: Types.EkyBlockingErrorSetting;
      // backend populates
      upload_url?: string;
      log_url?: string;
      face_actions?: string;
      ensure_facecompare?: boolean;
      auto_next?: boolean | { value: boolean; delay?: number };
      camera_rotate_value?: number;
      camera_rotate_field?: string;
      camera_zoom_value?: number;
      camera_zoom_field?: string;
      camera_flip_value?: boolean;
      camera_flip_field?: string;
      items?: {
        preview?: ISchemaItem;
        passed?: ISchemaItem;
      };
      fields?: {
        preview?: string;
        passed?: string;
        camera_side?: string;
      };
    };

  type ISchemaItemEkycDocumentPreviewWrapper = ISchemaItem & {
    type: 'EkycDocumentPreviewWrapper';
    document_type: string;
  };

  type ISchemaItemEkycLiveness = ISchemaItemEkyc &
    Partial<LivenessComponentSetting> & {
      type:
        | 'Ekyc.Liveness'
        | 'Ekyc.LivenessV2'
        | 'Ekyc.LivenessV3'
        | 'Ekyc.LivenessV4'
        | 'Ekyc.LivenessVNext';
    };

  type ISchemaItemEkycDocument = ISchemaItemEkyc &
    Partial<DocumentComponentSetting> & {
      type:
        | 'Ekyc.Document'
        | 'Ekyc.DocumentVNext'
        | 'Ekyc.FrontCard'
        | 'Ekyc.Passport'
        | 'Ekyc.DriverLicense'
        | 'Ekyc.ResidencePermit'
        | 'Ekyc.Portrait'
        | 'Ekyc.ThaiAlienCard'
        | 'Ekyc.BackCard'
        | 'Ekyc.BackCardVNext';
      selector_field: string;
      configs?: {
        ignore_face?: boolean;
        ignore_ocr?: boolean;
        check_expiry?: boolean;
        check_id_match_with?: string;
        check_ocr_fields?: {
          field: string;
          document_type?: string;
        }[];
        check_age?: {
          min?: number;
          max?: number;
          field?: 'age' | string;
        };
        check_warning?: boolean | string[];
        enabled_vertical_experience?: boolean;
      };
      autofill_map?: {
        src: string;
        dest: string;
        document_type?: EkycDocumentItemTypes;
        params?: {
          document_type?: EkycDocumentItemTypes;
          flag_name?: string;
          flag_equal?: string | number | boolean;
        };
      }[];
      items?: {
        ekyc_document_type?: ISchemaItemChoice<EkycDocumentItemTypes>;
        ekyc_document_country?: ISchemaItemChoice<string>;
      };
      include_nfc?: boolean;
      fields?: {
        country: string | false;
        document_type: string | false;
        preview: string | false;
        document_numbers: string | false;
        name_prefix: string | false;
        full_name: string | false;
        date_of_birth: string | false;
        date_of_issue: string | false;
        date_of_expiry: string | false;
        home_address: string | false;
        gender: string | false;
        full_name_en_first_name: string | false;
        full_name_en_last_name: string | false;
        ekyc_backcard_item?: string | false;
      };
    };
}
