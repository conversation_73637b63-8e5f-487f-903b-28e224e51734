export function copyToClipboard(text: string) {
  navigator.clipboard.writeText(text).then(
    () => {
      console.log('Async: Copying to clipboard was successful!');
    },
    err => {
      console.error('Async: Could not copy text: ', err);
    },
  );
}

export async function copyToClipboardFromElement(element: HTMLInputElement) {
  element.select();
  element.setSelectionRange(0, 99999); /* For mobile devices */

  try {
    const text = element.value;
    await navigator.clipboard.writeText(text);
    console.log('Copying text command was successful');
    return true;
  } catch (err) {
    console.log('Oops, unable to copy', err);
    return false;
  }
}
