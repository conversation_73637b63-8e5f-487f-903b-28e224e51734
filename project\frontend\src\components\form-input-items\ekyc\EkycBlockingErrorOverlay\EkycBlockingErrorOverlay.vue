<script setup lang="ts">
import { useDynamicEvent } from '@core/composables/use-dynamic-event';

import ImageSelector from '@helpers/components/ImageSelector.vue';
import { useI18n } from '@helpers/helpers/vue-i18n-composable';

import { playShowOverlay } from '@ekyc/helpers/animations';

import { resolveUrl } from '@/helpers/router-utils';

const props = defineProps<{
  overlaySetting: Types.EkyBlockingErrorSetting;
}>();

const emit = defineEmits<(e: 'yes') => void>();

const { t } = useI18n();

const { runActions } = useDynamicEvent('app.form');
const overrideMaxAttemptImages = computed(() => props.overlaySetting?.override_images || {});

const buttonVisible = computed(() => props.overlaySetting?.button?.visible);
const buttonLabel = computed(() => props.overlaySetting?.button?.label);

async function onClick() {
  const target = props.overlaySetting?.button?.target;
  const actions = props.overlaySetting?.button?.actions;
  if (Array.isArray(actions)) {
    console.log('[Run button actions]:', actions);
    await runActions(actions);
  }

  if (typeof target === 'string') {
    const resolvedTarget = resolveUrl(target);
    console.log('[TargetButton] To:', resolvedTarget);
    window.location.href = resolvedTarget;
  }
}

function processRedirect() {
  if (props.overlaySetting?.button?.visible && props.overlaySetting?.button?.auto_redirect) {
    onClick();
  }
}

onMounted(() => {
  playShowOverlay();
  processRedirect();
});
</script>

<template>
  <div class="modal blocking-error-overlay is-active">
    <div class="modal-background" />
    <div class="modal-card">
      <img
        v-if="overrideMaxAttemptImages['operator']"
        :src="overrideMaxAttemptImages['operator']"
        class="preview"
        alt="failed"
      />
      <ImageSelector v-else name="operator" style="max-height: 164px; max-width: 164px" />

      <div class="flex flex-col gap-2 items-center">
        <div class="blocking-error-title">{{ t('ekyc.recorder.error_blocking') }}</div>
        <div class="blocking-error-desc">{{ t('ekyc.recorder.error_blocking_desc') }}</div>
      </div>

      <button v-if="buttonVisible" class="button blocking-error-btn" @click.prevent="onClick">
        {{ buttonLabel }}
      </button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.modal {
  @apply p-6;
}

.modal-background {
  background-color: rgba(40, 48, 57, 0.86) !important;
}

.modal-card {
  @apply w-full p-6 rounded-2xl;
  @apply flex flex-col gap-6;
  opacity: 0px;
}

.modal-footer-action {
  @apply absolute px-6 bottom-4;
  @apply w-full;
}

.blocking-error-btn {
  @apply w-full !max-w-[unset] h-[48px];
  @apply text-[var(--app-button-text-color)];
  @apply bg-[var(--app-button-background-color)];

  &:hover,
  &:focus,
  &:active {
    @apply text-[var(--app-button-text-color)];
  }
}

.blocking-error-title {
  @apply text-[18px] font-bold leading-[26px] items-center;
}

.blocking-error-desc {
  @apply text-sm font-medium leading-6 items-center;
  color: var(--color-text-light, #777b8b);
}

.blocking-error-overlay .preview {
  width: 120px;
  margin: 0 auto;
}
</style>
