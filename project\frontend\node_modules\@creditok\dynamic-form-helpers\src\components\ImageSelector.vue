<template>
  <figure
    style="text-align: var(--alignment); overflow: hidden"
    :class="`image-selector img-${name}`"
    @click="emit('click')"
    @keyup.enter="emit('click')"
  >
    <template v-for="(path, key) in IMAGE_PATHS">
      <div v-if="props.name === key" :key="key" class="w-full h-full">
        <img v-svg-inline :src="path" :alt="key" class="element-image" :style="styling" />
      </div>
    </template>
  </figure>
</template>

<script setup lang="ts">
const emit = defineEmits<(e: 'click') => void>();

import.meta.glob('../assets/img/*.{svg}', { eager: true });

const NAME_LIST = [
  'email-verification',
  'email-verification-otp',

  'mobile-verification',
  'mobile-verification-otp',

  'multiple-documents',
  'multiple-documents-frame',
  'multiple-documents-pass',
  'multiple-documents-fail',
  'multiple-documents-error',

  'capture-button',

  'id-card',
  'id-card-frame',
  'id-card-no-frame',
  'id-card-pass',
  'id-card-fail',
  'id-card-error',
  'id-card-orientation-error',
  'id-card-grey',

  'front-card-preview',
  'passport-preview',
  'thai-alien-card-preview',
  'work-permit-card-preview',
  'work-permit-book-preview',
  'white-card-preview',
  'monk-card-preview',
  'immigration-card-preview',
  'other-document-preview',
  'back-card-preview',

  'thai-alien-card',
  'thai-alien-card-frame',
  // 'thai-alien-card-no-frame',
  'thai-alien-card-pass',
  'thai-alien-card-fail',
  'thai-alien-card-error',
  'thai-alien-card-orientation-error',
  'thai-alien-card-grey',

  'passport',
  'passport-frame',
  'passport-no-frame',
  'passport-pass',
  'passport-fail',
  'passport-error',

  'driver-license',
  'driver-license-frame',
  'driver-license-no-frame',
  'driver-license-pass',
  'driver-license-fail',
  'driver-license-error',

  'residence-permit',
  'residence-permit-frame',
  'residence-permit-no-frame',
  'residence-permit-pass',
  'residence-permit-fail',
  'residence-permit-error',

  'ci-passport',
  'ci-passport-pass',
  'ci-passport-fail',
  'ci-passport-error',

  'work-permit-card',
  'work-permit-card-pass',
  'work-permit-card-fail',
  'work-permit-card-error',

  'work-permit-book',
  'work-permit-book-pass',
  'work-permit-book-fail',
  'work-permit-book-error',

  'travel-document',
  'travel-document-pass',
  'travel-document-fail',
  'travel-document-error',

  'white-card',
  'white-card-pass',
  'white-card-fail',
  'white-card-error',

  'border-pass',
  'border-pass-pass',
  'border-pass-fail',
  'border-pass-error',

  'monk-card',
  'monk-card-pass',
  'monk-card-fail',
  'monk-card-error',

  'immigration-card',
  'immigration-card-pass',
  'immigration-card-fail',
  'immigration-card-error',

  'other-document',
  'other-document-pass',
  'other-document-fail',
  'other-document-error',

  'supported-country-error',
  'face-error',
  'ocr-fields-error',
  'passport-orientation-error',
  'passport-mrz-error',
  'fraud-error',
  'nfc-error',
  'nfc',
  'nfc-required',
  'nfc-permission-required',

  'selfie',
  'selfie-error',
  'selfie-face-cartoon-idle',
  'selfie-face-cartoon-turn-right',

  'liveness-instruction-glasses',
  'liveness-instruction-light-bad',
  'liveness-instruction-light-good',
  'liveness-instruction-mask',
  'liveness-instruction-multi-face',

  'check',
  'cross-device',
  'failed-timeout',
  'id-focus-frame',
  'id-focus-frame_bl',
  'id-focus-frame_br',
  'id-focus-frame_tl',
  'id-focus-frame_tr',
  'phone-call',
  'telephone',
  'success',

  'pdf',
  'add-document',
  'bank-plus',
  'error-address',
  'error-calendar',
  'error-card',
  'error-cloud',
  'error-document',
  'error-key',
  'error-pdf',
  'file-plus',
  'key',
  'id-card-preview',
  'liveness-preview',
  'no-activity',
  'work-time',
  'operator',

  'utility-bill',
  'utility-bill-electricity',
  'utility-bill-telco',
  'utility-bill-water',

  'browser-chrome',
  'browser-safari',
  'browser-generic',
  'browser-update',
] as const;

const IMAGE_PATHS = NAME_LIST.reduce(
  (prev, cur) => ({
    ...prev,
    [cur]: new URL(`../assets/img/${cur}.svg`, import.meta.url).href,
  }),
  {} as Record<PossibleNames, string>,
);

type PossibleNames = (typeof NAME_LIST)[number];

const props = defineProps({
  name: {
    type: String as () => PossibleNames,
    required: true,
  },
  overrideStyle: {
    type: String,
    require: false,
    default: '',
  },
});

const styling = computed(
  () => props.overrideStyle || 'width: 100%; height: 100%; object-fit: contain;',
);
</script>

<style src="@helpers/assets/css/work-time.css"></style>
<style src="@helpers/assets/css/nfc.css"></style>
