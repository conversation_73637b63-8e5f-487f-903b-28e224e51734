export const useEkycOverlayPopup = () => {
  const overlayPopupState = ref<
    /* Common */
    | ''
    | 'camera_permission'
    | 'camera_retry'
    | 'max_attempt'
    | 'unfocus_app'
    | 'blocking_error'
    /* Liveness */
    | 'fail'
    | 'timeout'
    /* Document */
    | 'card_flip'
    | 'loading'
  >('');

  function setOverlayPopupState(val: typeof overlayPopupState.value) {
    console.log('setOverlayPopupState', val);
    overlayPopupState.value = val;
  }

  return {
    overlayPopupState: readonly(overlayPopupState),
    setOverlayPopupState,
  };
};

export default { useEkycOverlayPopup };
