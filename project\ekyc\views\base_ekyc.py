from pydash import get, pick, map_values
from rest_framework import viewsets

BLOCKING_ERROR_KEYS = ["fraud_prevention_detection", "known_face_prevention"]


class BaseEkycViewset(viewsets.GenericViewSet):
    def build_response(self, response={}, ekyc=None, error_type=None, call_webhook_max_attempt=True):
        built_response = {
            **pick(
                response,
                "data",
                "result",
                "status",
                "code",
                "ocr",
                "warning",
                "document_type",
                "result_url",
                "compare_url",
                "preview_url",
            ),
        }
        ### * Resolve error type
        # find key in result that has status=false and key in ends with blocking
        result: dict = get(response, ["data", "result"], {})
        blocking_key = next(
            (key for key, value in result.items() if value.get("status") == False and key in BLOCKING_ERROR_KEYS),
            None,
        )
        # if blocking_key is found, set error_type to blocking
        if blocking_key:
            error_type = "blocking"
        # set error_type to response
        if error_type:
            built_response["error_type"] = error_type

        ### * Resolve attempt
        if ekyc:
            built_response["attempt_count"] = getattr(ekyc, f"{self.model_name}_attempt_count")
            built_response["attempt_left"] = getattr(ekyc, f"{self.model_name}_attempt_left")
            max_attempt = not getattr(ekyc, f"{self.model_name}_has_attempt")
            if max_attempt:
                if call_webhook_max_attempt:
                    action = f"{self.model_name}_reached_max_attempts"
                    ekyc.set_application_drop_off(action=f"ekyc_{action}")
                    ekyc.call_webhook(action)
                built_response["max_attempt"] = True

        if type(response) == dict and response.get("warning"):
            built_response["warning"] = map_values(response["warning"], lambda x: pick(x, "pass", "status"))
        return built_response
